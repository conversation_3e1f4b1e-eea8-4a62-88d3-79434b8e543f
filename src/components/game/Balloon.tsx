import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Balloon } from '../../entities/BalloonEntity';

interface BalloonProps {
  balloon: Balloon;
  onPop?: () => void;
}

export const BalloonComponent: React.FC<BalloonProps> = ({ balloon }) => {
  // Don't render popped balloons
  if (balloon.isPopped) {
    return null;
  }

  return (
    <View
      style={[
        styles.balloon,
        {
          left: balloon.position.x - balloon.type.size / 2,
          top: balloon.position.y - balloon.type.size / 2,
          width: balloon.type.size,
          height: balloon.type.size,
          backgroundColor: balloon.type.color,
        },
      ]}
    >
      {/* Balloon highlight */}
      <View style={[styles.highlight, {
        width: balloon.type.size * 0.3,
        height: balloon.type.size * 0.3,
      }]} />

      {/* Special balloon indicator */}
      {balloon.type.special && (
        <View
          style={[
            styles.specialIndicator,
            {
              width: balloon.type.size * 0.8,
              height: balloon.type.size * 0.8,
            },
          ]}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  balloon: {
    position: 'absolute',
    borderRadius: 1000, // Make it circular
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  highlight: {
    position: 'absolute',
    top: '20%',
    left: '25%',
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    borderRadius: 1000,
  },
  specialIndicator: {
    position: 'absolute',
    top: '10%',
    left: '10%',
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    borderRadius: 1000,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.8)',
  },
});
