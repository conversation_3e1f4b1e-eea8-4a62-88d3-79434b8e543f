import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
  Text,
  Pressable
} from 'react-native';
import { GameEngine } from '../../utils/GameEngine';
import { GameState } from '../../types/GameTypes';
import { BalloonComponent } from './Balloon';
import { HUD } from '../ui/HUD';
// import { ParticleSystem, createPopParticles } from '../effects/ParticleSystem';
// import { FloatingScore } from '../effects/FloatingScore';
import { DebugLogger } from '../../utils/DebugLogger';
import { i18n } from '../../utils/i18n';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface GameBoardProps {
  currentWorld?: number;
  currentLevel?: number;
  onGameComplete?: (score: number) => void;
  onBackPress?: () => void;
}

export const GameBoard: React.FC<GameBoardProps> = ({
  currentWorld = 1,
  currentLevel = 1,
  onGameComplete,
  onBackPress,
}) => {
  const gameEngineRef = useRef<GameEngine | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const lastTimeRef = useRef<number>(Date.now());
  const [translations, setTranslations] = useState(i18n.getTranslations());

  const [gameState, setGameState] = useState<GameState>({
    score: 0,
    level: 1,
    balloons: [],
    isPlaying: false,
    timeRemaining: 60000,
  });

  useEffect(() => {
    const handleLanguageChange = () => {
      setTranslations(i18n.getTranslations());
    };

    i18n.addLanguageChangeListener(handleLanguageChange);

    return () => {
      i18n.removeLanguageChangeListener(handleLanguageChange);
    };
  }, []);

  // const [particles, setParticles] = useState<any[]>([]);
  // const [floatingScores, setFloatingScores] = useState<Array<{
  //   id: string;
  //   x: number;
  //   y: number;
  //   points: number;
  //   combo: number;
  // }>>([]);

  useEffect(() => {
    // Initialize game engine
    gameEngineRef.current = new GameEngine(SCREEN_WIDTH, SCREEN_HEIGHT);
    gameEngineRef.current.setStateChangeCallback(setGameState);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  const startGameLoop = React.useCallback(() => {
    try {
      DebugLogger.gameEvent('Starting game loop');
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      lastTimeRef.current = Date.now();

      const gameLoop = () => {
        try {
          const currentTime = Date.now();
          const deltaTime = (currentTime - lastTimeRef.current) / 1000; // Convert to seconds
          lastTimeRef.current = currentTime;

          if (gameEngineRef.current) {
            gameEngineRef.current.update(deltaTime);
          }

          // Continue the loop only if we still have a valid animation frame reference
          if (animationFrameRef.current !== null) {
            animationFrameRef.current = requestAnimationFrame(gameLoop);
          }
        } catch (error) {
          DebugLogger.error('Error in game loop', error);
          // Stop the game loop on error to prevent infinite error loops
          if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current);
            animationFrameRef.current = null;
          }
        }
      };

      animationFrameRef.current = requestAnimationFrame(gameLoop);
      DebugLogger.gameEvent('Game loop started');
    } catch (error) {
      DebugLogger.error('Error starting game loop', error);
    }
  }, []);

  const stopGameLoop = React.useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
  }, []);

  useEffect(() => {
    if (gameState.isPlaying) {
      startGameLoop();
    } else {
      stopGameLoop();
    }
  }, [gameState.isPlaying, startGameLoop, stopGameLoop]);

  const handleTouch = (event: any) => {
    try {
      const { pageX, pageY, locationX, locationY } = event.nativeEvent || {};

      // Use pageX/pageY for absolute screen coordinates, fallback to locationX/locationY
      const x = pageX ?? locationX ?? 0;
      const y = pageY ?? locationY ?? 0;

      if (gameEngineRef.current && gameState.isPlaying && typeof x === 'number' && typeof y === 'number') {
        DebugLogger.gameEvent('Processing touch (absolute coords)', { x, y, pageX, pageY, locationX, locationY });
        gameEngineRef.current.handleTouchInput(x, y);
      }
    } catch (error) {
      console.warn('Touch handling error:', error);
    }
  };

  // const removeParticle = (particleId: string) => {
  //   setParticles(prev => prev.filter(p => p.id !== particleId));
  // };

  // const removeFloatingScore = (scoreId: string) => {
  //   setFloatingScores(prev => prev.filter(s => s.id !== scoreId));
  // };

  const startGame = () => {
    try {
      DebugLogger.gameEvent('Starting game');
      if (gameEngineRef.current) {
        // setParticles([]); // Clear any existing particles
        // setFloatingScores([]); // Clear any existing floating scores
        DebugLogger.gameEvent('Game engine starting');
        gameEngineRef.current.start();
        DebugLogger.gameEvent('Game started successfully');
      } else {
        DebugLogger.error('Game engine not initialized');
      }
    } catch (error) {
      DebugLogger.error('Error starting game', error);
    }
  };

  const handleGameComplete = () => {
    if (onGameComplete) {
      onGameComplete(gameState.score);
    }
  };

  const renderGameOverScreen = () => (
    <View style={styles.gameOverContainer}>
      <Text style={styles.gameOverTitle}>{translations.levelComplete}</Text>
      <Text style={styles.levelInfo}>{translations.world} {currentWorld} - {translations.level} {currentLevel}</Text>
      <Text style={styles.finalScore}>{translations.finalScore}: {gameState.score.toLocaleString()}</Text>

      <View style={styles.buttonContainer}>
        <Pressable style={styles.playButton} onPress={startGame}>
          <Text style={styles.playButtonText}>{translations.playAgain}</Text>
        </Pressable>

        {onGameComplete && (
          <Pressable style={[styles.playButton, styles.nextButton]} onPress={handleGameComplete}>
            <Text style={styles.playButtonText}>{translations.nextLevel}</Text>
          </Pressable>
        )}

        {onBackPress && (
          <Pressable style={[styles.playButton, styles.backButton]} onPress={onBackPress}>
            <Text style={styles.playButtonText}>{translations.backToLevels}</Text>
          </Pressable>
        )}
      </View>
    </View>
  );

  const renderStartScreen = () => (
    <View style={styles.startContainer}>
      {onBackPress && (
        <Pressable style={styles.topBackButton} onPress={onBackPress}>
          <Text style={styles.topBackButtonText}>{translations.back}</Text>
        </Pressable>
      )}

      <View style={styles.startContent}>
        <Text style={styles.title}>{translations.world} {currentWorld}</Text>
        <Text style={styles.subtitle}>{translations.level} {currentLevel}</Text>
        <Text style={styles.instructions}>{translations.tapBalloonsToPopThem}</Text>
        <Text style={styles.speedWarning}>{translations.fastMode}</Text>

        <Pressable style={styles.playButton} onPress={startGame}>
          <Text style={styles.playButtonText}>{translations.startLevel}</Text>
        </Pressable>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <TouchableWithoutFeedback onPress={handleTouch}>
        <View style={styles.gameArea}>
          {/* Background gradient effect */}
          <View style={styles.background} />

          {/* HUD */}
          {gameState.isPlaying && (
            <HUD
              gameState={gameState}
              combo={gameEngineRef.current?.getCombo() || 0}
            />
          )}

          {/* Balloons */}
          {gameEngineRef.current?.getBalloons().map((balloon) => (
            <BalloonComponent
              key={balloon.id}
              balloon={balloon}
            />
          ))}

          {/* Simplified Effects - Temporarily disabled to prevent crashes */}
          {/*
          <ParticleSystem
            particles={particles}
            onParticleComplete={removeParticle}
          />

          {floatingScores.map((score) => (
            <FloatingScore
              key={score.id}
              x={score.x}
              y={score.y}
              points={score.points}
              combo={score.combo}
              onComplete={() => removeFloatingScore(score.id)}
            />
          ))}
          */}

          {/* Game screens */}
          {!gameState.isPlaying && gameState.timeRemaining === 60000 && renderStartScreen()}
          {!gameState.isPlaying && gameState.timeRemaining < 60000 && renderGameOverScreen()}
        </View>
      </TouchableWithoutFeedback>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gameArea: {
    flex: 1,
    position: 'relative',
  },
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#87CEEB', // Sky blue
  },
  startContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  gameOverContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2E86AB',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 30,
    textAlign: 'center',
  },
  gameOverTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
    textAlign: 'center',
  },
  levelInfo: {
    fontSize: 18,
    color: '#fff',
    marginBottom: 20,
    textAlign: 'center',
    opacity: 0.8,
  },
  finalScore: {
    fontSize: 24,
    color: '#fff',
    marginBottom: 30,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    gap: 15,
  },
  playButton: {
    backgroundColor: '#2E86AB',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  playButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  nextButton: {
    backgroundColor: '#4CAF50',
  },
  backButton: {
    backgroundColor: '#FF5722',
  },
  topBackButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    backgroundColor: 'rgba(46, 134, 171, 0.8)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  topBackButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  startContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructions: {
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
    textAlign: 'center',
  },
  speedWarning: {
    fontSize: 14,
    color: '#FF5722',
    marginBottom: 30,
    textAlign: 'center',
    fontWeight: 'bold',
  },
});
