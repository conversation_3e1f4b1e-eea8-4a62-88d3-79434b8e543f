import { Balloon } from '../entities/BalloonEntity';
import { GAME_CONFIG } from '../types/GameTypes';

export class PhysicsSystem {
  private screenWidth: number;
  private screenHeight: number;
  private windForce: number = 0;
  private windDirection: number = 1; // 1 for right, -1 for left
  private windChangeTime: number = 0;
  private readonly WIND_CHANGE_INTERVAL = 5000; // Change wind every 5 seconds

  constructor(screenWidth: number, screenHeight: number) {
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;
    this.updateWind();
  }

  private updateWind(): void {
    // Generate random wind force and direction
    this.windForce = Math.random() * 30; // 0-30 pixels per second
    this.windDirection = Math.random() > 0.5 ? 1 : -1;
    this.windChangeTime = Date.now();
  }

  public updateBalloons(balloons: Balloon[], deltaTime: number): void {
    // Update wind periodically
    if (Date.now() - this.windChangeTime > this.WIND_CHANGE_INTERVAL) {
      this.updateWind();
    }

    balloons.forEach(balloon => {
      if (!balloon.isPopped) {
        // Apply wind force
        balloon.velocity.x += this.windForce * this.windDirection * deltaTime * 0.1;

        balloon.update(deltaTime);
        this.applyBoundaries(balloon);
      }
    });
  }

  private applyBoundaries(balloon: Balloon): void {
    // Keep balloons within horizontal boundaries
    if (balloon.position.x - balloon.radius < 0) {
      balloon.position.x = balloon.radius;
      balloon.velocity.x = Math.abs(balloon.velocity.x);
    } else if (balloon.position.x + balloon.radius > this.screenWidth) {
      balloon.position.x = this.screenWidth - balloon.radius;
      balloon.velocity.x = -Math.abs(balloon.velocity.x);
    }
  }

  public removeOffScreenBalloons(balloons: Balloon[]): Balloon[] {
    return balloons.filter(balloon => !balloon.isOffScreen(this.screenHeight));
  }

  public spawnBalloon(): Balloon {
    const x = Math.random() * (this.screenWidth - 60) + 30; // 30px margin
    const y = this.screenHeight + 30; // Start below screen
    return new Balloon(x, y);
  }

  public updateScreenDimensions(width: number, height: number): void {
    this.screenWidth = width;
    this.screenHeight = height;
  }

  public getWindInfo(): { force: number; direction: number } {
    return {
      force: this.windForce,
      direction: this.windDirection,
    };
  }
}
