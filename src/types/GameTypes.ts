// Core game types and interfaces

export interface Position {
  x: number;
  y: number;
}

export interface Velocity {
  x: number;
  y: number;
}

export interface BalloonType {
  id: string;
  color: string;
  points: number;
  size: number;
  mass: number; // Mass affects speed and collision physics
  special?: boolean;
}

export interface BalloonEntity {
  id: string;
  type: BalloonType;
  position: Position;
  velocity: Velocity;
  isPopped: boolean;
  createdAt: number;
}

export interface GameState {
  score: number;
  level: number;
  balloons: BalloonEntity[];
  isPlaying: boolean;
  timeRemaining: number;
}

export interface TouchEvent {
  x: number;
  y: number;
  timestamp: number;
}

// Balloon types configuration
export const BALLOON_TYPES: { [key: string]: BalloonType } = {
  RED: {
    id: 'red',
    color: '#FF6B6B',
    points: 10,
    size: 55, // Small balloon
    mass: 0.8, // Lighter = slower
  },
  BLUE: {
    id: 'blue',
    color: '#4ECDC4',
    points: 10,
    size: 60, // Medium balloon
    mass: 1.0, // Base mass
  },
  GREEN: {
    id: 'green',
    color: '#45B7D1',
    points: 10,
    size: 65, // Large balloon
    mass: 1.2, // Heavier = faster
  },
  YELLOW: {
    id: 'yellow',
    color: '#FFA07A',
    points: 10,
    size: 58, // Medium-small balloon
    mass: 0.9, // Slightly lighter
  },
  GOLD: {
    id: 'gold',
    color: '#FFD700',
    points: 50,
    size: 70, // Extra large balloon
    mass: 1.3, // Heaviest = fastest
    special: true,
  },
  SILVER: {
    id: 'silver',
    color: '#C0C0C0',
    points: 25,
    size: 62, // Medium-large balloon
    mass: 1.1, // Heavier than base
    special: true,
  },
};

export const GAME_CONFIG = {
  BALLOON_SPAWN_RATE: 333, // milliseconds (3x faster - was 1000)
  BALLOON_FLOAT_SPEED: 150, // pixels per second (3x faster - was 50)
  GAME_DURATION: 60000, // 60 seconds
  SCREEN_WIDTH: 375, // will be updated with actual screen dimensions
  SCREEN_HEIGHT: 812, // will be updated with actual screen dimensions
};
