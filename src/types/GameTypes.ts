// Core game types and interfaces

export interface Position {
  x: number;
  y: number;
}

export interface Velocity {
  x: number;
  y: number;
}

export interface BalloonType {
  id: string;
  color: string;
  points: number;
  size: number;
  mass: number; // Mass affects speed and collision physics
  special?: boolean;
}

export interface BalloonEntity {
  id: string;
  type: BalloonType;
  position: Position;
  velocity: Velocity;
  isPopped: boolean;
  createdAt: number;
}

export interface GameState {
  score: number;
  level: number;
  balloons: BalloonEntity[];
  isPlaying: boolean;
  timeRemaining: number;
}

export interface TouchEvent {
  x: number;
  y: number;
  timestamp: number;
}

// Balloon types configuration
export const BALLOON_TYPES: { [key: string]: BalloonType } = {
  RED: {
    id: 'red',
    color: '#FF6B6B',
    points: 10,
    size: 45, // Smallest balloon (75% smaller than largest)
    mass: 0.5, // Lightest = slowest (75% slower than fastest)
  },
  BLUE: {
    id: 'blue',
    color: '#4ECDC4',
    points: 10,
    size: 60, // Medium balloon
    mass: 1.0, // Base mass
  },
  GREEN: {
    id: 'green',
    color: '#45B7D1',
    points: 10,
    size: 70, // Large balloon
    mass: 1.5, // Heavy = fast
  },
  YELLOW: {
    id: 'yellow',
    color: '#FFA07A',
    points: 10,
    size: 52, // Small-medium balloon
    mass: 0.75, // Light = slow
  },
  GOLD: {
    id: 'gold',
    color: '#FFD700',
    points: 50,
    size: 80, // Largest balloon (75% larger than smallest)
    mass: 1.75, // Heaviest = fastest (75% faster than slowest)
    special: true,
  },
  SILVER: {
    id: 'silver',
    color: '#C0C0C0',
    points: 25,
    size: 65, // Medium-large balloon
    mass: 1.25, // Heavy = fast
    special: true,
  },
};

export const GAME_CONFIG = {
  BALLOON_SPAWN_RATE: 333, // milliseconds (3x faster - was 1000)
  BALLOON_FLOAT_SPEED: 150, // pixels per second (3x faster - was 50)
  GAME_DURATION: 60000, // 60 seconds
  SCREEN_WIDTH: 375, // will be updated with actual screen dimensions
  SCREEN_HEIGHT: 812, // will be updated with actual screen dimensions
};
